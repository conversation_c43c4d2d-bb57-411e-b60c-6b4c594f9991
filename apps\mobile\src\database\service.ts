import { database, getRoomId } from './config';
import { Q } from '@nozbe/watermelondb';
import { Chat, MessageType } from './models/Chat';
import { Room } from './models/Room';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

// Get collections
const chatsCollection = database.get<Chat>('chats');
const roomsCollection = database.get<Room>('rooms');

// Database service for chat operations
export const chatDBService = {
  // Initialize the database
  initialize: async (): Promise<boolean> => {
    try {
      // Perform a simple write operation to ensure the database is working
      await database.write(async () => {
        // Just check if we can access the collections
        const chats = await chatsCollection.query().fetch();
        const rooms = await roomsCollection.query().fetch();
        console.log('Database initialized with', chats.length, 'chats and', rooms.length, 'rooms');
      });
      return true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      return false;
    }
  },

  // Add a new message (for sent messages only)
  addMessage: async (
    currentUserId: string,
    receiverId: string,
    message: string,
    type: MessageType = 'text',
    status: string = 'sent'
  ): Promise<Chat> => {
    try {
      const roomId = getRoomId(currentUserId, receiverId);
      const timestamp = Date.now();

      const newMessage = await database.write(async () => {
        // Create the message (this is always for sent messages)
        const chat = await chatsCollection.create(chatRecord => {
          chatRecord.roomId = roomId;
          chatRecord.senderUsername = currentUserId;
          chatRecord.receiverUsername = receiverId;
          chatRecord.message = message;
          chatRecord.type = type;
          chatRecord.timestamp = timestamp;
          chatRecord.status = status;
          chatRecord.isMine = true; // This method is only for sent messages
        });

        // Update or create the room within the same transaction
        const existingRooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', receiverId)
        ).fetch();

        if (existingRooms.length > 0) {
          // Update existing room
          await existingRooms[0].update(room => {
            room.lastMsg = message;
            room.updated = timestamp;
          });
        } else {
          // Create new room
          await roomsCollection.create(room => {
            room.roomId = roomId;
            room.username = receiverId;
            room.lastMsg = message;
            room.updated = timestamp;
            room.unreadCount = 0;
          });
        }

        return chat;
      });

      return newMessage;
    } catch (error) {
      console.error('Failed to add message:', error);
      throw error;
    }
  },

  // Get or create a room
  getOrCreateRoom: async (currentUserId: string, otherUserId: string): Promise<Room> => {
    try {
      const roomId = getRoomId(currentUserId, otherUserId);

      // Check if room exists
      const existingRoom = await roomsCollection.query(
        Q.where('room_id', roomId),
        Q.where('username', otherUserId)
      ).fetch();

      if (existingRoom.length > 0) {
        return existingRoom[0];
      }

      // Create new room using database.write
      const newRoom = await database.write(async () => {
        return await roomsCollection.create(room => {
          room.roomId = roomId;
          room.username = otherUserId;
          room.lastMsg = '';
          room.updated = Date.now();
          room.unreadCount = 0;
        });
      });

      return newRoom;
    } catch (error) {
      console.error('Failed to get or create room:', error);
      throw error;
    }
  },

  // Update room with latest message
  updateRoom: async (
    currentUserId: string,
    otherUserId: string,
    lastMessage: string,
    timestamp: number
  ): Promise<void> => {
    try {
      const roomId = getRoomId(currentUserId, otherUserId);

      await database.write(async () => {
        // Find existing room
        const existingRooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', otherUserId)
        ).fetch();

        if (existingRooms.length > 0) {
          // Update existing room
          await existingRooms[0].update(room => {
            room.lastMsg = lastMessage;
            room.updated = timestamp;
          });
        } else {
          // Create new room
          await roomsCollection.create(room => {
            room.roomId = roomId;
            room.username = otherUserId;
            room.lastMsg = lastMessage;
            room.updated = timestamp;
            room.unreadCount = 0;
          });
        }
      });
    } catch (error) {
      console.error('Failed to update room:', error);
      throw error;
    }
  },

  // Get all messages for a conversation
  getMessages: async (currentUserId: string, otherUserId: string): Promise<Record<string, any>[]> => {
    try {
      const roomId = getRoomId(currentUserId, otherUserId);

      const messages = await chatsCollection.query(
        Q.where('room_id', roomId),
        Q.sortBy('timestamp', Q.asc)
      ).fetch();

      return messages.map(message => message.toJSON());
    } catch (error) {
      console.error('Failed to get messages:', error);
      return [];
    }
  },

  // Get a message by its ID (for client_message_id matching)
  getMessageById: async (messageId: string): Promise<Record<string, any> | null> => {
    try {
      const message = await chatsCollection.find(messageId);
      return message.toJSON();
    } catch (error) {
      // Message not found or other error
      console.log(`Message with ID ${messageId} not found`);
      return null;
    }
  },

  // Observe messages for a conversation (reactive)
  observeMessages: (currentUserId: string, otherUserId: string): Observable<Record<string, any>[]> => {
    const roomId = getRoomId(currentUserId, otherUserId);

    return chatsCollection.query(
      Q.where('room_id', roomId),
      Q.sortBy('timestamp', Q.asc)
    ).observe()
    .pipe(
      map(messages => messages.map(message => message.toJSON()))
    );
  },

  // Get all rooms for a user
  getRooms: async (username: string): Promise<Record<string, any>[]> => {
    try {
      const rooms = await roomsCollection.query(
        Q.or(
          Q.where('username', username),
          Q.where('room_id', Q.like(`%${username}%`))
        ),
        Q.sortBy('updated', Q.desc)
      ).fetch();

      return rooms.map(room => room.toJSON());
    } catch (error) {
      console.error('Failed to get rooms:', error);
      return [];
    }
  },

  // Observe rooms for a user (reactive)
  observeRooms: (username: string): Observable<Record<string, any>[]> => {
    return roomsCollection.query(
      Q.or(
        Q.where('username', username),
        Q.where('room_id', Q.like(`%${username}%`))
      ),
      Q.sortBy('updated', Q.desc)
    ).observe()
    .pipe(
      map(rooms => rooms.map(room => room.toJSON()))
    );
  },

  // Clear all data (for testing)
  clearAllData: async (): Promise<void> => {
    try {
      await database.write(async () => {
        await database.unsafeResetDatabase();
      });
      console.log('Database cleared');
    } catch (error) {
      console.error('Failed to clear database:', error);
      throw error;
    }
  },

  // Save a message (alias for addMessage to match frontend API)
  saveMessage: async (
    senderUsername: string,
    receiverUsername: string,
    message: string,
    isMine: boolean,
    type: MessageType = 'text',
    status: string,
    currentReceiverUsername?: string | null
  ): Promise<Chat> => {
    try {
      const roomId = getRoomId(senderUsername, receiverUsername);
      const timestamp = Date.now();

      const newMessage = await database.write(async () => {
        // Create the message
        const chat = await chatsCollection.create(chatRecord => {
          chatRecord.roomId = roomId;
          chatRecord.senderUsername = senderUsername;
          chatRecord.receiverUsername = receiverUsername;
          chatRecord.message = message;
          chatRecord.type = type;
          chatRecord.timestamp = timestamp;
          chatRecord.status = status;
          chatRecord.isMine = isMine;
        });

        // Handle room creation/update within the same transaction
        if (!isMine) {
          // For received messages: current user is receiver, sender is the other user
          const otherUser = senderUsername;

          // Find existing room
          const existingRooms = await roomsCollection.query(
            Q.where('room_id', roomId),
            Q.where('username', otherUser)
          ).fetch();

          if (existingRooms.length > 0) {
            // Update existing room and increment unread count
            await existingRooms[0].update(room => {
              room.lastMsg = message;
              room.updated = timestamp;
              room.unreadCount = (room.unreadCount || 0) + 1;
            });
          } else {
            // Create new room with unread count = 1
            await roomsCollection.create(room => {
              room.roomId = roomId;
              room.username = otherUser;
              room.lastMsg = message;
              room.updated = timestamp;
              room.unreadCount = 1;
            });
          }
        } else {
          // For sent messages: current user is sender, receiver is the other user
          const otherUser = receiverUsername;

          // Find existing room
          const existingRooms = await roomsCollection.query(
            Q.where('room_id', roomId),
            Q.where('username', otherUser)
          ).fetch();

          if (existingRooms.length > 0) {
            // Update existing room (don't change unread count for sent messages)
            await existingRooms[0].update(room => {
              room.lastMsg = message;
              room.updated = timestamp;
            });
          } else {
            // Create new room (unread count = 0 for sent messages)
            await roomsCollection.create(room => {
              room.roomId = roomId;
              room.username = otherUser;
              room.lastMsg = message;
              room.updated = timestamp;
              room.unreadCount = 0;
            });
          }
        }

        return chat;
      });

      return newMessage;
    } catch (error) {
      console.error('Failed to save message:', error);
      throw error;
    }
  },

  // Update message status
  updateMessageStatus: async (messageId: string, status: string): Promise<void> => {
    try {
      await database.write(async () => {
        const message = await chatsCollection.find(messageId);
        await message.update(chat => {
          chat.status = status;
        });
      });
    } catch (error) {
      console.error('Failed to update message status:', error);
      throw error;
    }
  },

  // Clear room messages
  clearRoom: async (currentUser: string, otherUser: string): Promise<void> => {
    try {
      const roomId = getRoomId(currentUser, otherUser);
      const timestamp = Date.now();

      await database.write(async () => {
        // Delete all messages in the room
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId)
        ).fetch();

        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Update room with clear message within the same transaction
        const existingRooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', otherUser)
        ).fetch();

        if (existingRooms.length > 0) {
          // Update existing room
          await existingRooms[0].update(room => {
            room.lastMsg = 'Chat cleared';
            room.updated = timestamp;
          });
        } else {
          // Create new room
          await roomsCollection.create(room => {
            room.roomId = roomId;
            room.username = otherUser;
            room.lastMsg = 'Chat cleared';
            room.updated = timestamp;
            room.unreadCount = 0;
          });
        }
      });
    } catch (error) {
      console.error('Failed to clear room:', error);
      throw error;
    }
  },

  // Send clear chat message
  sendClearChatMessage: async (senderUsername: string, receiverUsername: string): Promise<void> => {
    try {
      await chatDBService.addMessage(senderUsername, receiverUsername, 'Chat cleared', 'clear_chat', 'sent');
    } catch (error) {
      console.error('Failed to send clear chat message:', error);
      throw error;
    }
  },

  // Delete user room
  deleteUserRoom: async (currentUser: string, otherUser: string): Promise<void> => {
    try {
      const roomId = getRoomId(currentUser, otherUser);

      await database.write(async () => {
        // Delete all messages in the room
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId)
        ).fetch();

        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Delete the room
        const rooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', otherUser)
        ).fetch();

        for (const room of rooms) {
          await room.destroyPermanently();
        }
      });
    } catch (error) {
      console.error('Failed to delete user room:', error);
      throw error;
    }
  },

  // Mark messages as read
  markMessagesAsRead: async (currentUser: string, otherUser: string): Promise<void> => {
    try {
      const roomId = getRoomId(currentUser, otherUser);

      await database.write(async () => {
        // Mark all unread messages as read
        const unreadMessages = await chatsCollection.query(
          Q.where('room_id', roomId),
          Q.where('is_mine', false),
          Q.where('status', Q.notEq('read'))
        ).fetch();

        for (const message of unreadMessages) {
          await message.update(chat => {
            chat.status = 'read';
          });
        }

        // Reset unread count in room
        const rooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', otherUser)
        ).fetch();

        for (const room of rooms) {
          await room.update(roomRecord => {
            roomRecord.unreadCount = 0;
          });
        }
      });
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
      throw error;
    }
  },

  // Add some sample data for testing
  addSampleData: async (currentUserId: string): Promise<void> => {
    try {
      const sampleUsers = ['alice', 'bob', 'charlie'];
      const sampleMessages = [
        'Hello there!',
        'How are you doing?',
        'Great to hear from you!',
        'Let\'s catch up soon',
        'Thanks for the message'
      ];

      for (const user of sampleUsers) {
        if (user !== currentUserId) {
          // Create a room and add some messages
          await chatDBService.getOrCreateRoom(currentUserId, user);

          // Add a few sample messages
          for (let i = 0; i < 2; i++) {
            const message = sampleMessages[Math.floor(Math.random() * sampleMessages.length)];
            await chatDBService.addMessage(currentUserId, user, message);
          }
        }
      }

      console.log('Sample data added');
    } catch (error) {
      console.error('Failed to add sample data:', error);
      throw error;
    }
  },
};
