<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0">
  <diagram name="System Architecture" id="system-arch-1">
    <mxGraphModel dx="1042" dy="1696" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="mobile" value="Mobile App (React Native)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="60" y="80" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="web" value="Web Frontend (React)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="60" y="180" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="admin" value="Admin Panel (React)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="60" y="280" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api" value="Backend API (NestJS)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="320" y="160" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="db" value="Database" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="580" y="90" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="media" value="Media Storage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="580" y="290" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fcm" value="Push Notification Service (FCM)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#000;" parent="1" vertex="1">
          <mxGeometry x="310" y="-70" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mobile-api" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;endFill=1;strokeColor=#6c8ebf;" parent="1" source="mobile" target="api" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="web-api" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;endFill=1;strokeColor=#6c8ebf;" parent="1" source="web" target="api" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="admin-api" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;endFill=1;strokeColor=#6c8ebf;" parent="1" source="admin" target="api" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="api-db" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;endFill=1;strokeColor=#82b366;" parent="1" source="api" target="db" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="api-media" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;endFill=1;strokeColor=#b85450;" parent="1" source="api" target="media" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="api-fcm" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" parent="1" source="api" target="fcm" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
