{"artifacts": [{"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./RNPermissionsSpec-generated.cpp.o"}, {"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp.o"}, {"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./react/renderer/components/RNPermissionsSpec/EventEmitters.cpp.o"}, {"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./react/renderer/components/RNPermissionsSpec/Props.cpp.o"}, {"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./1ac44fb29917a513567e7eceb3c4dfa7/RNPermissionsSpecJSI-generated.cpp.o"}, {"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o"}, {"path": "RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./react/renderer/components/RNPermissionsSpec/States.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957", "name": "react_codegen_RNPermissionsSpec", "paths": {"build": "RNPermissionsSpec_autolinked_build", "source": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/RNPermissionsSpec-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/projecttest/chatspot-messenger/apps/mobile/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/States.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}