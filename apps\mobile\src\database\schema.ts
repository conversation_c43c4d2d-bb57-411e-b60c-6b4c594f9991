import { appSchema, tableSchema } from '@nozbe/watermelondb';

export const schema = appSchema({
  version: 2, // Updated to version 2 to add client_message_id
  tables: [
    // Chats table - represents individual messages
    tableSchema({
      name: 'chats',
      columns: [
        { name: 'room_id', type: 'string', isIndexed: true },
        { name: 'sender_username', type: 'string', isIndexed: true },
        { name: 'receiver_username', type: 'string', isIndexed: true },
        { name: 'message', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'timestamp', type: 'number', isIndexed: true },
        { name: 'status', type: 'string' },
        { name: 'is_mine', type: 'boolean' },
        { name: 'client_message_id', type: 'string', isOptional: true, isIndexed: true },
      ]
    }),

    // Rooms table - represents active conversations
    tableSchema({
      name: 'rooms',
      columns: [
        { name: 'room_id', type: 'string', isIndexed: true },
        { name: 'username', type: 'string', isIndexed: true },
        { name: 'last_msg', type: 'string' },
        { name: 'updated', type: 'number', isIndexed: true },
        { name: 'unread_count', type: 'number' },
      ]
    }),
  ]
});
