<mxfile host="app.diagrams.net" agent="AI" version="27.2.0">
  <diagram name="Send Message Sequence" id="send-message-seq-1">
    <mxGraphModel dx="1000" dy="600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Lifelines -->
        <mxCell id="user" value="User" style="swimlane;horizontal=0;startSize=20;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="60" width="80" height="600" as="geometry" />
        </mxCell>
        <mxCell id="frontend" value="Frontend (Mobile/Web)" style="swimlane;horizontal=0;startSize=20;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="180" y="60" width="120" height="600" as="geometry" />
        </mxCell>
        <mxCell id="backend" value="Backend API (NestJS)" style="swimlane;horizontal=0;startSize=20;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="340" y="60" width="120" height="600" as="geometry" />
        </mxCell>
        <mxCell id="db" value="Database" style="swimlane;horizontal=0;startSize=20;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="500" y="60" width="100" height="600" as="geometry" />
        </mxCell>
        <mxCell id="media" value="Media Storage" style="swimlane;horizontal=0;startSize=20;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="640" y="60" width="120" height="600" as="geometry" />
        </mxCell>
        <mxCell id="fcm" value="Push Notification Service (FCM)" style="swimlane;horizontal=0;startSize=20;fillColor=#fff;strokeColor=#000;" vertex="1" parent="1">
          <mxGeometry x="800" y="60" width="180" height="600" as="geometry" />
        </mxCell>
        <mxCell id="recipient" value="Recipient" style="swimlane;horizontal=0;startSize=20;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="1020" y="60" width="80" height="600" as="geometry" />
        </mxCell>
        <!-- Messages -->
        <mxCell id="msg1" value="Type and send message" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="user" target="frontend">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="140" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="msg2" value="Send message (REST/WebSocket)" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="frontend" target="backend">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="260" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="msg3" value="Save message" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="backend" target="db">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Media upload (optional) -->
        <mxCell id="msg4" value="Upload media file" style="edgeStyle=elbowEdgeStyle;dashed=1;endArrow=block;endFill=1;strokeColor=#b85450;" edge="1" parent="1" source="backend" target="media">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="msg5" value="Return media URL" style="edgeStyle=elbowEdgeStyle;dashed=1;endArrow=block;endFill=1;strokeColor=#b85450;" edge="1" parent="1" source="media" target="backend">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="700" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="msg6" value="Save media URL in message" style="edgeStyle=elbowEdgeStyle;dashed=1;endArrow=block;endFill=1;strokeColor=#b85450;" edge="1" parent="1" source="backend" target="db">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Push notification -->
        <mxCell id="msg7" value="Send push notification to recipient" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="backend" target="fcm">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="900" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="msg8" value="Deliver notification" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="fcm" target="recipient">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1100" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Recipient fetches message -->
        <mxCell id="msg9" value="Fetch new message" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="recipient" target="backend">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="700" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="msg10" value="Return message data" style="edgeStyle=elbowEdgeStyle;endArrow=block;endFill=1;strokeColor=#000;" edge="1" parent="1" source="backend" target="recipient">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1100" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 