<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0">
  <diagram name="Page-1" id="cjuqLEDIeJOSimrxHTLO">
    <mxGraphModel dx="1042" dy="527" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Rooms Table -->
        <mxCell id="roomsTable" value="rooms" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;html=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="200" width="220" height="210" as="geometry" />
        </mxCell>
        <mxCell id="roomsHeader" value="rooms" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;fontStyle=1;" vertex="1" parent="roomsTable">
          <mxGeometry y="30" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rooms_id" value="PK id (auto)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="roomsHeader">
          <mxGeometry x="0" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rooms_room_id" value="room_id (string, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="roomsTable">
          <mxGeometry y="60" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rooms_username" value="username (string, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="roomsTable">
          <mxGeometry y="90" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rooms_last_msg" value="last_msg (string)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="roomsTable">
          <mxGeometry y="120" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rooms_updated" value="updated (number, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="roomsTable">
          <mxGeometry y="150" width="220" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rooms_unread_count" value="unread_count (number)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="roomsTable">
          <mxGeometry y="180" width="220" height="30" as="geometry" />
        </mxCell>
        <!-- Chats Table -->
        <mxCell id="chatsTable" value="chats" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;html=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="120" width="320" height="510" as="geometry" />
        </mxCell>
        <mxCell id="chatsHeader" value="chats" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;fontStyle=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="30" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_id" value="PK id (auto)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsHeader">
          <mxGeometry x="0" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_room_id" value="room_id (string, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="60" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_sender_username" value="sender_username (string, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="90" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_receiver_username" value="receiver_username (string, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="120" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_message" value="message (string)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="150" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_type" value="type (string)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="180" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_timestamp" value="timestamp (number, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="210" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_status" value="status (string)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="240" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_is_mine" value="is_mine (boolean)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="270" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_client_message_id" value="client_message_id (string, optional, indexed)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="300" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_id" value="media_id (string, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="330" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_url" value="media_url (string, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="360" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_type" value="media_type (string, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="390" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_filename" value="media_filename (string, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="420" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_file_size" value="media_file_size (number, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="450" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_local_path" value="media_local_path (string, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="480" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_thumbnail_path" value="media_thumbnail_path (string, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="510" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_width" value="media_width (number, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="540" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_height" value="media_height (number, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="570" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="chats_media_duration" value="media_duration (number, optional)" style="shape=partialRectangle;connectable=0;fillColor=none;align=left;spacingLeft=6;overflow=hidden;whiteSpace=wrap;html=1;" vertex="1" parent="chatsTable">
          <mxGeometry y="600" width="320" height="30" as="geometry" />
        </mxCell>
        <!-- Relationship Arrow -->
        <mxCell id="relArrow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="rooms_room_id" target="chats_room_id">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
